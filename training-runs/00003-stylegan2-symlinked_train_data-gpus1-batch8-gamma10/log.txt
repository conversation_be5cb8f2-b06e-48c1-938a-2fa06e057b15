Loading training set...

Num images:  21174
Image shape: [1, 768, 768]
Label shape: [36]

Constructing networks...
Traceback (most recent call last):
  File "/mnt/data/ajohn/stylegan3/train.py", line 286, in <module>
    main() # pylint: disable=no-value-for-parameter
  File "/mnt/data/ajohn/miniconda3/envs/stylegan3/lib/python3.9/site-packages/click/core.py", line 1161, in __call__
    return self.main(*args, **kwargs)
  File "/mnt/data/ajohn/miniconda3/envs/stylegan3/lib/python3.9/site-packages/click/core.py", line 1082, in main
    rv = self.invoke(ctx)
  File "/mnt/data/ajohn/miniconda3/envs/stylegan3/lib/python3.9/site-packages/click/core.py", line 1443, in invoke
    return ctx.invoke(self.callback, **ctx.params)
  File "/mnt/data/ajohn/miniconda3/envs/stylegan3/lib/python3.9/site-packages/click/core.py", line 788, in invoke
    return __callback(*args, **kwargs)
  File "/mnt/data/ajohn/stylegan3/train.py", line 281, in main
    launch_training(c=c, desc=desc, outdir=opts.outdir, dry_run=opts.dry_run)
  File "/mnt/data/ajohn/stylegan3/train.py", line 96, in launch_training
    subprocess_fn(rank=0, c=c, temp_dir=temp_dir)
  File "/mnt/data/ajohn/stylegan3/train.py", line 47, in subprocess_fn
    training_loop.training_loop(rank=rank, **c)
  File "/mnt/data/ajohn/stylegan3/training/training_loop.py", line 152, in training_loop
    G = dnnlib.util.construct_class_by_name(**G_kwargs, **common_kwargs).train().requires_grad_(False).to(device) # subclass of torch.nn.Module
  File "/mnt/data/ajohn/stylegan3/dnnlib/util.py", line 303, in construct_class_by_name
    return call_func_by_name(*args, func_name=class_name, **kwargs)
  File "/mnt/data/ajohn/stylegan3/dnnlib/util.py", line 298, in call_func_by_name
    return func_obj(*args, **kwargs)
  File "/mnt/data/ajohn/stylegan3/torch_utils/persistence.py", line 104, in __init__
    super().__init__(*args, **kwargs)
  File "/mnt/data/ajohn/stylegan3/training/networks_stylegan2.py", line 543, in __init__
    self.synthesis = SynthesisNetwork(w_dim=w_dim, img_resolution=img_resolution, img_channels=img_channels, **synthesis_kwargs)
  File "/mnt/data/ajohn/stylegan3/torch_utils/persistence.py", line 104, in __init__
    super().__init__(*args, **kwargs)
  File "/mnt/data/ajohn/stylegan3/training/networks_stylegan2.py", line 477, in __init__
    assert img_resolution >= 4 and img_resolution & (img_resolution - 1) == 0
AssertionError
