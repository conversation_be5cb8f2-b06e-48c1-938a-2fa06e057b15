import os
import csv

pairs_txt = "/mnt/data/ajohn/stylegan3/datasets/extracted_path/paths_and_genes.txt"
symlinks_root = "/mnt/data/ajohn/stylegan3/datasets/eye2gene_new_filepaths/symlinked_train_data"

os.makedirs(symlinks_root, exist_ok=True)

with open(pairs_txt) as f:
    reader = csv.reader(f)
    for img_path, gene in reader:
        gene = gene.strip()
        img_path = img_path.strip()
        
        class_dir = os.path.join(symlinks_root, gene)
        os.makedirs(class_dir, exist_ok=True)
        
        file_name = os.path.basename(img_path)
        link_name = os.path.join(class_dir, file_name)
        if not os.path.exists(link_name):
            try:
                os.symlink(img_path, link_name)
            except FileExistsError:
                pass
