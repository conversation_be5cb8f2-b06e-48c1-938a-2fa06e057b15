import csv
import os

input_csv  = '/mnt/data/ajohn/stylegan3/datasets/eye2gene_new_filepaths/all_baf_valid_50deg_filtered_train_0_edited.csv'
output_txt = '/mnt/data/ajohn/stylegan3/datasets/extracted_path/paths_and_genes.txt'

with open(input_csv, newline='') as f_in:
    reader = csv.reader(f_in)
    header = next(reader)
    idx_path = header.index('file.path')
    idx_gene = header.index('gene')
    
    with open(output_txt, 'w', newline='') as f_out:
        writer = csv.writer(f_out)
        for row in reader:
            img_path = row[idx_path].strip()
            gene = row[idx_gene].strip()
            if img_path and gene:
                writer.writerow([img_path, gene])
