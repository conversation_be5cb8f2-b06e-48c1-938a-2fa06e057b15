#!/usr/bin/env python3

import os
import json
import zipfile
from collections import defaultdict

def create_dataset_json_from_symlinks():
    """
    Create a dataset.json file for the ZIP archive based on the original symlink structure.
    This maps the flattened img00000000.png files back to their gene classes.
    """
    
    # Path to the symlinked data (original structure)
    symlink_path = "./datasets/eye2gene_new_filepaths/symlinked_train_data"
    
    # Path to the ZIP file
    zip_path = "./datasets/eye2gene_symlinked_512x512.zip"
    
    # Get the original file structure with gene labels
    print("Reading original symlink structure...")
    gene_to_files = defaultdict(list)
    
    for gene_dir in sorted(os.listdir(symlink_path)):
        gene_path = os.path.join(symlink_path, gene_dir)
        if os.path.isdir(gene_path):
            files = sorted([f for f in os.listdir(gene_path) if f.endswith('.png')])
            gene_to_files[gene_dir] = files
            print(f"  {gene_dir}: {len(files)} files")
    
    # Create sorted list of genes (this will be our label mapping)
    sorted_genes = sorted(gene_to_files.keys())
    gene_to_label = {gene: idx for idx, gene in enumerate(sorted_genes)}
    
    print(f"\nFound {len(sorted_genes)} gene classes:")
    for gene, label in gene_to_label.items():
        print(f"  {label}: {gene}")
    
    # Read the ZIP file to get the flattened file list
    print(f"\nReading ZIP file: {zip_path}")
    with zipfile.ZipFile(zip_path, 'r') as z:
        zip_files = sorted([f for f in z.namelist() if f.endswith('.png')])
    
    print(f"Found {len(zip_files)} PNG files in ZIP")
    
    # Create the mapping from flattened files to labels
    # The dataset_tool.py processes files in alphabetical order by gene, then by filename
    labels = []
    file_to_label = {}
    
    file_idx = 0
    for gene in sorted_genes:
        gene_files = sorted(gene_to_files[gene])
        gene_label = gene_to_label[gene]
        
        for original_file in gene_files:
            if file_idx < len(zip_files):
                zip_file = zip_files[file_idx]
                file_to_label[zip_file] = gene_label
                labels.append([zip_file, gene_label])
                file_idx += 1
    
    print(f"\nCreated {len(labels)} file-to-label mappings")
    
    # Create the dataset.json structure
    dataset_json = {
        "labels": labels
    }
    
    # Save the dataset.json
    json_path = "dataset.json"
    with open(json_path, 'w') as f:
        json.dump(dataset_json, f, indent=2)
    
    print(f"\nCreated {json_path} with {len(labels)} labels")
    
    # Add the dataset.json to the ZIP file
    print(f"Adding dataset.json to {zip_path}...")
    with zipfile.ZipFile(zip_path, 'a') as z:
        z.write(json_path, json_path)
    
    print("Done! The ZIP file now contains dataset.json with proper gene labels.")
    
    # Clean up
    os.remove(json_path)
    
    # Print summary
    print(f"\nSummary:")
    print(f"  Total images: {len(labels)}")
    print(f"  Total classes: {len(sorted_genes)}")
    print(f"  Class distribution:")
    
    class_counts = defaultdict(int)
    for _, label in labels:
        class_counts[label] += 1
    
    for gene_idx, gene in enumerate(sorted_genes):
        count = class_counts[gene_idx]
        print(f"    {gene_idx:2d} ({gene:8s}): {count:4d} images")

if __name__ == "__main__":
    create_dataset_json_from_symlinks()
