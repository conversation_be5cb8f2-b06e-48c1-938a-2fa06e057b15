Loading training set...

Num images:  10500
Image shape: [1, 256, 256]
Label shape: [0]

Constructing networks...
Setting up PyTorch plugin "bias_act_plugin"... Failed!
Traceback (most recent call last):
  File "/mnt/data/ajohn/miniconda3/envs/stylegan3/lib/python3.9/site-packages/torch/utils/cpp_extension.py", line 1666, in _run_ninja_build
    subprocess.run(
  File "/mnt/data/ajohn/miniconda3/envs/stylegan3/lib/python3.9/subprocess.py", line 528, in run
    raise CalledProcessError(retcode, process.args,
subprocess.CalledProcessError: Command '['ninja', '-v']' returned non-zero exit status 1.

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/mnt/data/ajohn/stylegan3/train.py", line 286, in <module>
    main() # pylint: disable=no-value-for-parameter
  File "/mnt/data/ajohn/miniconda3/envs/stylegan3/lib/python3.9/site-packages/click/core.py", line 1161, in __call__
    return self.main(*args, **kwargs)
  File "/mnt/data/ajohn/miniconda3/envs/stylegan3/lib/python3.9/site-packages/click/core.py", line 1082, in main
    rv = self.invoke(ctx)
  File "/mnt/data/ajohn/miniconda3/envs/stylegan3/lib/python3.9/site-packages/click/core.py", line 1443, in invoke
    return ctx.invoke(self.callback, **ctx.params)
  File "/mnt/data/ajohn/miniconda3/envs/stylegan3/lib/python3.9/site-packages/click/core.py", line 788, in invoke
    return __callback(*args, **kwargs)
  File "/mnt/data/ajohn/stylegan3/train.py", line 281, in main
    launch_training(c=c, desc=desc, outdir=opts.outdir, dry_run=opts.dry_run)
  File "/mnt/data/ajohn/stylegan3/train.py", line 96, in launch_training
    subprocess_fn(rank=0, c=c, temp_dir=temp_dir)
  File "/mnt/data/ajohn/stylegan3/train.py", line 47, in subprocess_fn
    training_loop.training_loop(rank=rank, **c)
  File "/mnt/data/ajohn/stylegan3/training/training_loop.py", line 168, in training_loop
    img = misc.print_module_summary(G, [z, c])
  File "/mnt/data/ajohn/stylegan3/torch_utils/misc.py", line 216, in print_module_summary
    outputs = module(*inputs)
  File "/mnt/data/ajohn/miniconda3/envs/stylegan3/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1071, in _call_impl
    result = forward_call(*input, **kwargs)
  File "/mnt/data/ajohn/stylegan3/training/networks_stylegan2.py", line 548, in forward
    ws = self.mapping(z, c, truncation_psi=truncation_psi, truncation_cutoff=truncation_cutoff, update_emas=update_emas)
  File "/mnt/data/ajohn/miniconda3/envs/stylegan3/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1071, in _call_impl
    result = forward_call(*input, **kwargs)
  File "/mnt/data/ajohn/stylegan3/training/networks_stylegan2.py", line 246, in forward
    x = layer(x)
  File "/mnt/data/ajohn/miniconda3/envs/stylegan3/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1071, in _call_impl
    result = forward_call(*input, **kwargs)
  File "/mnt/data/ajohn/stylegan3/training/networks_stylegan2.py", line 124, in forward
    x = bias_act.bias_act(x, b, act=self.activation)
  File "/mnt/data/ajohn/stylegan3/torch_utils/ops/bias_act.py", line 84, in bias_act
    if impl == 'cuda' and x.device.type == 'cuda' and _init():
  File "/mnt/data/ajohn/stylegan3/torch_utils/ops/bias_act.py", line 41, in _init
    _plugin = custom_ops.get_plugin(
  File "/mnt/data/ajohn/stylegan3/torch_utils/custom_ops.py", line 136, in get_plugin
    torch.utils.cpp_extension.load(name=module_name, build_directory=cached_build_dir,
  File "/mnt/data/ajohn/miniconda3/envs/stylegan3/lib/python3.9/site-packages/torch/utils/cpp_extension.py", line 1080, in load
    return _jit_compile(
  File "/mnt/data/ajohn/miniconda3/envs/stylegan3/lib/python3.9/site-packages/torch/utils/cpp_extension.py", line 1293, in _jit_compile
    _write_ninja_file_and_build_library(
  File "/mnt/data/ajohn/miniconda3/envs/stylegan3/lib/python3.9/site-packages/torch/utils/cpp_extension.py", line 1405, in _write_ninja_file_and_build_library
    _run_ninja_build(
  File "/mnt/data/ajohn/miniconda3/envs/stylegan3/lib/python3.9/site-packages/torch/utils/cpp_extension.py", line 1682, in _run_ninja_build
    raise RuntimeError(message) from e
RuntimeError: Error building extension 'bias_act_plugin': [1/2] /usr/bin/nvcc  -DTORCH_EXTENSION_NAME=bias_act_plugin -DTORCH_API_INCLUDE_EXTENSION_H -DPYBIND11_COMPILER_TYPE=\"_gcc\" -DPYBIND11_STDLIB=\"_libstdcpp\" -DPYBIND11_BUILD_ABI=\"_cxxabi1011\" -isystem /mnt/data/ajohn/miniconda3/envs/stylegan3/lib/python3.9/site-packages/torch/include -isystem /mnt/data/ajohn/miniconda3/envs/stylegan3/lib/python3.9/site-packages/torch/include/torch/csrc/api/include -isystem /mnt/data/ajohn/miniconda3/envs/stylegan3/lib/python3.9/site-packages/torch/include/TH -isystem /mnt/data/ajohn/miniconda3/envs/stylegan3/lib/python3.9/site-packages/torch/include/THC -isystem /mnt/data/ajohn/miniconda3/envs/stylegan3/include/python3.9 -D_GLIBCXX_USE_CXX11_ABI=0 -D__CUDA_NO_HALF_OPERATORS__ -D__CUDA_NO_HALF_CONVERSIONS__ -D__CUDA_NO_BFLOAT16_CONVERSIONS__ -D__CUDA_NO_HALF2_OPERATORS__ --expt-relaxed-constexpr -gencode=arch=compute_86,code=compute_86 -gencode=arch=compute_86,code=sm_86 --compiler-options '-fPIC' --use_fast_math --allow-unsupported-compiler -std=c++14 -c /home/<USER>/.cache/torch_extensions/bias_act_plugin/3cb576a0039689487cfba59279dd6d46-nvidia-rtx-a6000/bias_act.cu -o bias_act.cuda.o 
FAILED: bias_act.cuda.o 
/usr/bin/nvcc  -DTORCH_EXTENSION_NAME=bias_act_plugin -DTORCH_API_INCLUDE_EXTENSION_H -DPYBIND11_COMPILER_TYPE=\"_gcc\" -DPYBIND11_STDLIB=\"_libstdcpp\" -DPYBIND11_BUILD_ABI=\"_cxxabi1011\" -isystem /mnt/data/ajohn/miniconda3/envs/stylegan3/lib/python3.9/site-packages/torch/include -isystem /mnt/data/ajohn/miniconda3/envs/stylegan3/lib/python3.9/site-packages/torch/include/torch/csrc/api/include -isystem /mnt/data/ajohn/miniconda3/envs/stylegan3/lib/python3.9/site-packages/torch/include/TH -isystem /mnt/data/ajohn/miniconda3/envs/stylegan3/lib/python3.9/site-packages/torch/include/THC -isystem /mnt/data/ajohn/miniconda3/envs/stylegan3/include/python3.9 -D_GLIBCXX_USE_CXX11_ABI=0 -D__CUDA_NO_HALF_OPERATORS__ -D__CUDA_NO_HALF_CONVERSIONS__ -D__CUDA_NO_BFLOAT16_CONVERSIONS__ -D__CUDA_NO_HALF2_OPERATORS__ --expt-relaxed-constexpr -gencode=arch=compute_86,code=compute_86 -gencode=arch=compute_86,code=sm_86 --compiler-options '-fPIC' --use_fast_math --allow-unsupported-compiler -std=c++14 -c /home/<USER>/.cache/torch_extensions/bias_act_plugin/3cb576a0039689487cfba59279dd6d46-nvidia-rtx-a6000/bias_act.cu -o bias_act.cuda.o 
/usr/include/c++/11/bits/std_function.h:435:145: error: parameter packs not expanded with ‘...’:
  435 |         function(_Functor&& __f)
      |                                                                                                                                                 ^ 
/usr/include/c++/11/bits/std_function.h:435:145: note:         ‘_ArgTypes’
/usr/include/c++/11/bits/std_function.h:530:146: error: parameter packs not expanded with ‘...’:
  530 |         operator=(_Functor&& __f)
      |                                                                                                                                                  ^ 
/usr/include/c++/11/bits/std_function.h:530:146: note:         ‘_ArgTypes’
ninja: build stopped: subcommand failed.

